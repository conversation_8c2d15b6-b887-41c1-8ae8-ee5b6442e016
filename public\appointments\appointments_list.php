<?php
require_once 'config.php';

$appointments = getAppointments();
$statusColors = [
    'Scheduled' => 'bg-blue-100 text-blue-800',
    'Completed' => 'bg-green-100 text-green-800',
    'Cancelled' => 'bg-red-100 text-red-800',
    'No Show' => 'bg-yellow-100 text-yellow-800'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments List</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="max-w-6xl mx-auto py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold">Appointments</h1>
            <a href="appointment_form.php" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i> New Appointment
            </a>
        </div>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                Appointment created successfully!
            </div>
        <?php endif; ?>
        
        <div class="bg-white shadow-md rounded overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($appointments as $appointment): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap"><?= $appointment['id'] ?></td>
                        <td class="px-6 py-4 whitespace-nowrap"><?= htmlspecialchars($appointment['patient_name']) ?></td>
                        <td class="px-6 py-4 whitespace-nowrap"><?= htmlspecialchars($appointment['staff_name']) ?></td>
                        <td class="px-6 py-4 whitespace-nowrap"><?= $appointment['appointment_date'] ?></td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?= date('h:i A', strtotime($appointment['appointment_time'])) ?>
                        </td>
                        <td class="px-6 py-4"><?= htmlspecialchars($appointment['reason']) ?></td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs rounded-full <?= $statusColors[$appointment['status']] ?>">
                                <?= $appointment['status'] ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="view_appointment.php?id=<?= $appointment['id'] ?>" 
                               class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="edit_appointment.php?id=<?= $appointment['id'] ?>" 
                               class="text-green-600 hover:text-green-900 mr-3">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="delete_appointment.php?id=<?= $appointment['id'] ?>" 
                               class="text-red-600 hover:text-red-900"
                               onclick="return confirm('Are you sure you want to delete this appointment?');">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>