<?php
// config.php
$host = '127.0.0.1';
$dbname = 'edilvkyhospital';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

function getAppointments() {
    global $pdo;
    $sql = "SELECT a.*, 
                   CONCAT(p.first_name, ' ', p.last_name) AS patient_name,
                   CONCAT(s.first_name, ' ', s.last_name) AS staff_name
            FROM appointments a
            JOIN patients p ON a.patient_id = p.patient_id
            JOIN staff s ON a.staff_id = s.staff_id";
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function createAppointment($data) {
    global $pdo;
    $sql = "INSERT INTO appointments (patient_id, staff_id, appointment_date, appointment_time, reason, status) 
            VALUES (:patient_id, :staff_id, :appointment_date, :appointment_time, :reason, :status)";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute($data);
}

function getAppointmentById($id) {
    global $pdo;
    $sql = "SELECT a.*, 
                   CONCAT(p.first_name, ' ', p.last_name) AS patient_name,
                   CONCAT(s.first_name, ' ', s.last_name) AS staff_name
            FROM appointments a
            JOIN patients p ON a.patient_id = p.patient_id
            JOIN staff s ON a.staff_id = s.staff_id
            WHERE a.id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getAllPatients() {
    global $pdo;
    $stmt = $pdo->query("SELECT patient_id, CONCAT(first_name, ' ', last_name) AS full_name FROM patients");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getAllStaff() {
    global $pdo;
    $stmt = $pdo->query("SELECT staff_id, CONCAT(first_name, ' ', last_name) AS full_name FROM staff");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>