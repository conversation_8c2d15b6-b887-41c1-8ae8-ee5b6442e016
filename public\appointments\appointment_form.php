<?php
require_once 'config.php';

$patients = getAllPatients();
$staff_members = getAllStaff();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'patient_id' => $_POST['patient_id'],
        'staff_id' => $_POST['staff_id'],
        'appointment_date' => $_POST['appointment_date'],
        'appointment_time' => $_POST['appointment_time'],
        'reason' => $_POST['reason'],
        'status' => $_POST['status']
    ];
    
    if (createAppointment($data)) {
        header("Location: appointments_list.php?success=1");
        exit;
    } else {
        $error = "Failed to create appointment";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule Appointment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="max-w-4xl mx-auto py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Schedule New Appointment</h1>
        
        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <?= $error ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Patient Selection -->
                <div>
                    <label class="block text-gray-700 font-bold mb-2" for="patient_id">
                        Patient <span class="text-red-500">*</span>
                    </label>
                    <select 
                        name="patient_id" 
                        id="patient_id" 
                        required
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Select a patient...</option>
                        <?php foreach ($patients as $patient): ?>
                            <option value="<?= $patient['patient_id'] ?>">
                                <?= htmlspecialchars($patient['full_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Staff Selection -->
                <div>
                    <label class="block text-gray-700 font-bold mb-2" for="staff_id">
                        Staff Member <span class="text-red-500">*</span>
                    </label>
                    <select 
                        name="staff_id" 
                        id="staff_id" 
                        required
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Select a staff member...</option>
                        <?php foreach ($staff_members as $staff): ?>
                            <option value="<?= $staff['staff_id'] ?>">
                                <?= htmlspecialchars($staff['full_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Appointment Date -->
                <div>
                    <label class="block text-gray-700 font-bold mb-2" for="appointment_date">
                        Appointment Date <span class="text-red-500">*</span>
                    </label>
                    <input 
                        type="date" 
                        id="appointment_date" 
                        name="appointment_date" 
                        required
                        min="<?= date('Y-m-d') ?>"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
                
                <!-- Appointment Time -->
                <div>
                    <label class="block text-gray-700 font-bold mb-2" for="appointment_time">
                        Appointment Time <span class="text-red-500">*</span>
                    </label>
                    <input 
                        type="time" 
                        id="appointment_time" 
                        name="appointment_time" 
                        required
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
                
                <!-- Status -->
                <div>
                    <label class="block text-gray-700 font-bold mb-2" for="status">
                        Status
                    </label>
                    <select 
                        id="status" 
                        name="status"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="Scheduled">Scheduled</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                        <option value="No Show">No Show</option>
                    </select>
                </div>
            </div>
            
            <!-- Reason -->
            <div class="mt-6">
                <label class="block text-gray-700 font-bold mb-2" for="reason">
                    Reason <span class="text-red-500">*</span>
                </label>
                <textarea 
                    id="reason" 
                    name="reason" 
                    rows="3" 
                    required
                    placeholder="Enter the reason for this appointment"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
            </div>
            
            <!-- Submit Button -->
            <div class="flex items-center justify-between mt-8">
                <button 
                    type="submit"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Schedule Appointment
                </button>
            </div>
        </form>
    </div>
</body>
</html>