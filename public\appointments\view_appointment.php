<?php
require_once 'config.php';

$id = $_GET['id'] ?? null;

if (!$id) {
    die("Invalid appointment ID");
}

$appointment = getAppointmentById($id);

if (!$appointment) {
    die("Appointment not found");
}

$statusColors = [
    'Scheduled' => 'bg-blue-100 text-blue-800 border-blue-200',
    'Completed' => 'bg-green-100 text-green-800 border-green-200',
    'Cancelled' => 'bg-red-100 text-red-800 border-red-200',
    'No Show' => 'bg-yellow-100 text-yellow-800 border-yellow-200'
];
$statusClass = $statusColors[$appointment['status']] ?? 'bg-gray-100 text-gray-800';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appointment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="max-w-4xl mx-auto py-8">
        <a href="appointments_list.php" class="inline-block mb-6 text-blue-600 hover:text-blue-800">
            <i class="fas fa-arrow-left mr-2"></i> Back to Appointments
        </a>
        
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4 text-white">
                <h1 class="text-2xl font-bold">Appointment Details</h1>
                <p class="text-blue-100">ID: <?= $appointment['id'] ?></p>
            </div>
            
            <!-- Details -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h2 class="text-lg font-semibold mb-3">Patient Information</h2>
                        <p class="font-medium"><?= htmlspecialchars($appointment['patient_name']) ?></p>
                        <p class="text-gray-600">Patient ID: <?= $appointment['patient_id'] ?></p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h2 class="text-lg font-semibold mb-3">Staff Information</h2>
                        <p class="font-medium"><?= htmlspecialchars($appointment['staff_name']) ?></p>
                        <p class="text-gray-600">Staff ID: <?= $appointment['staff_id'] ?></p>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4 mb-6">
                    <h2 class="text-lg font-semibold mb-3">Appointment Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p><strong>Date:</strong> <?= $appointment['appointment_date'] ?></p>
                            <p><strong>Time:</strong> <?= date('h:i A', strtotime($appointment['appointment_time'])) ?></p>
                        </div>
                        <div>
                            <p><strong>Status:</strong> 
                                <span class="px-3 py-1 rounded-full text-sm <?= $statusClass ?>">
                                    <?= $appointment['status'] ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p><strong>Reason:</strong></p>
                        <p class="mt-2 p-3 bg-gray-50 rounded"><?= htmlspecialchars($appointment['reason']) ?></p>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <a href="edit_appointment.php?id=<?= $appointment['id'] ?>" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="appointments_list.php" 
                       class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-list mr-2"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>